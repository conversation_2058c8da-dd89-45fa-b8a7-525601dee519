"""
Bayesian Hyperparameter Tuning - Fast Testing Version
Reduced computational requirements for quick 30-trial testing
"""
import torch
import gc
import psutil
import os
import math
from predictive_coding.config import GPTConfig
from predictive_coding.pc_layer import PCLayer
from model_architecture.pc_t_model import PCTransformer
from Data_preprocessing.dataloader import train_loader, valid_loader
from training import train
from eval import evaluate
from utils.model_utils import reset_pc_modules, pad_collate_fn, load_tokenizer
from torch.utils.data import DataLoader, Subset
import logging
import time
import optuna
optuna.logging.set_verbosity(optuna.logging.WARNING)

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def cleanup_memory():
    """Comprehensive memory cleanup"""
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.synchronize()

def get_optimal_data_sizes():
    """Determine optimal data sizes - REDUCED FOR FAST TESTING"""
    # Significantly reduced for fast testing
    return 150, 30

def create_subset_loaders(batch_size):
    """Create appropriately sized data loaders"""
    tokenizer = load_tokenizer()
    pad_token_id = tokenizer.pad_token_id

    train_size, valid_size = get_optimal_data_sizes()
    max_train = len(train_loader.dataset)
    max_valid = len(valid_loader.dataset)

    train_size = min(train_size, max_train)
    valid_size = min(valid_size, max_valid)

    train_indices = torch.randperm(max_train)[:train_size]
    train_subset = Subset(train_loader.dataset, train_indices)

    valid_indices = torch.randperm(max_valid)[:valid_size]
    valid_subset = Subset(valid_loader.dataset, valid_indices)

    train_subset_loader = DataLoader(train_subset, batch_size=batch_size,
        shuffle=True, collate_fn=lambda batch: pad_collate_fn(batch, pad_token_id))

    valid_subset_loader = DataLoader(valid_subset, batch_size=batch_size,
        shuffle=False, collate_fn=lambda batch: pad_collate_fn(batch, pad_token_id))

    return train_subset_loader, valid_subset_loader

def get_dynamic_batch_size(n_embed, block_size):
    """Calculate optimal batch size - FIXED SMALL SIZE FOR FAST TESTING"""
    # Fixed small batch size for fast testing
    return 4

def normalize_energy(energy_value, energy_fn_name):
    """
    Normalize energy values to comparable scales across different energy functions.
    Based on empirical testing: MSE~1.86, scaled_MSE~0.09, KLD~9.02
    """
    normalization_factors = {
        'mse': 1.0,           # Reference scale
        'scaled_mse': 20.0,   # Scale up by ~20x to match MSE
        'kld': 0.2            # Scale down by ~5x to match MSE
    }
    factor = normalization_factors.get(energy_fn_name, 1.0)
    return energy_value * factor

def normalize_energy(energy_value, energy_fn_name):
    """
    Normalize energy values to comparable scales across different energy functions.
    Based on empirical testing: MSE~1.86, scaled_MSE~0.09, KLD~9.02
    """
    normalization_factors = {
        'mse': 1.0,           # Reference scale
        'scaled_mse': 20.0,   # Scale up by ~20x to match MSE
        'kld': 0.2            # Scale down by ~5x to match MSE
    }
    factor = normalization_factors.get(energy_fn_name, 1.0)
    return energy_value * factor

def update_global_config(config):
    """Update global GPTConfig to match trial config - CRITICAL for shape consistency"""
    GPTConfig.num_heads = config.num_heads
    GPTConfig.n_embed = config.n_embed
    GPTConfig.block_size = config.block_size
    GPTConfig.vocab_size = config.vocab_size
    GPTConfig.dropout = config.dropout
    GPTConfig.local_learning_rate = config.local_learning_rate
    GPTConfig.T = config.T
    GPTConfig.n_blocks = config.n_blocks
    GPTConfig.update_bias = config.update_bias
    GPTConfig.use_lateral = config.use_lateral
    GPTConfig.energy_fn_name = config.energy_fn_name
    
def get_dynamic_model_config(trial, vocab_size):
    """Get model configuration with FULL SEARCH SPACE - only data/computation reduced for speed"""
    # FULL search space preserved - same as original bayes_tuning.py
    n_embed_candidates = list(range(64, 769, 16))
    n_embed = n_embed_candidates[trial.suggest_int('embed_idx', 0, len(n_embed_candidates) - 1)]

    valid_heads = [h for h in range(4, min(16, n_embed // 12) + 1)
                if n_embed % h == 0 and 12 <= n_embed // h <= 128]

    if not valid_heads:
        valid_heads = [h for h in [4, 6, 8, 12, 16]
                    if h <= n_embed and n_embed % h == 0 and n_embed // h >= 8]
        if not valid_heads:
            if n_embed >= 48 and n_embed % 4 == 0:
                logger.warning(f"Forcing num_heads=4 for n_embed={n_embed} (head_dim={n_embed//4})")
            else:
                logger.warning(f"Skipping n_embed={n_embed} - cannot support minimum 4 heads")
                return None

    num_heads = valid_heads[trial.suggest_int('head_idx', 0, len(valid_heads) - 1)]
    block_size_candidates = list(range(64, 513, 16))
    block_size = block_size_candidates[trial.suggest_int('block_idx', 0, len(block_size_candidates)-1)]

    # FULL ranges preserved - same as original
    n_blocks = trial.suggest_int('n_blocks', 1, 6)
    T = trial.suggest_int('T', 4, 20)
    base_lr = trial.suggest_float('base_lr', 1e-5, 1e-3, log=True)
    scaled_lr = base_lr * (n_embed / 256) ** 0.5 * (block_size / 256) ** 0.25
    warmup_steps = trial.suggest_int('warmup_steps', 10, 100)

    # FULL energy function options preserved - including KLD that might cause NaN
    energy_fn_name = ['kld', 'mse', 'scaled_mse'][trial.suggest_int('energy_idx', 0, 2)]
    update_bias = trial.suggest_int('update_bias_int', 0, 1) == 1
    use_lateral = True
    head_dim = n_embed // num_heads
    
    logger.info(
    f"Params: n_embed={n_embed}, block_size={block_size}, num_heads={num_heads} (head_dim={head_dim}), "
    f"n_blocks={n_blocks}, T={T}, energy_fn={energy_fn_name}, update_bias={update_bias}, use_lateral={use_lateral}, "
    f"base_lr={base_lr:.2e}, scaled_lr={scaled_lr:.2e}, valid_heads={valid_heads}")
    
    return GPTConfig(
        vocab_size=vocab_size,
        block_size=block_size,
        n_embed=n_embed,
        dropout=trial.suggest_float('dropout', 0.05, 0.3),  # Full range preserved
        local_learning_rate=0.0,  # Will be set dynamically
        peak_learning_rate=scaled_lr,
        warmup_steps=warmup_steps,
        T=T,
        is_holding_error=True,
        num_heads=num_heads,
        n_blocks=n_blocks,
        num_epochs=1,
        update_bias=update_bias,
        use_lateral=use_lateral,
        energy_fn_name=energy_fn_name
    )

def objective(trial):
    """Bayesian Objective function - OPTIMIZED FOR SPEED"""
    start_time = time.time()
    model = None

    try:
        logger.info(f"Trial {trial.number}")
        cleanup_memory()
        tokenizer = load_tokenizer()
        vocab_size = tokenizer.vocab_size
        config = get_dynamic_model_config(trial, vocab_size)

        if config is None:
            logger.warning(f"Trial {trial.number} skipped - no valid config with min 4 heads")
            return float("inf")

        update_global_config(config)
        try:
            model = PCTransformer(config)            
        except Exception as e:
            logger.error(f"Model creation failed: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return float("inf")
        
        batch_size = get_dynamic_batch_size(config.n_embed, config.block_size)
        train_loader, valid_loader = create_subset_loaders(batch_size=batch_size)
        logger.info(f"Using batch size: {batch_size}, train_size: {len(train_loader.dataset)}, valid_size: {len(valid_loader.dataset)}")

        if len(train_loader) == 0:
            logger.error("Train loader is empty!")
            return float("inf")
        if len(valid_loader) == 0:
            logger.error("Valid loader is empty!")
            return float("inf")
        
        try:
            model.train()
            _, _ = train(model, train_loader, tokenizer)
        except Exception as e:
            logger.error(f"Training failed: {str(e)}")
            import traceback
            logger.error(f"Training traceback: {traceback.format_exc()}")
            return float("inf")
        
        reset_pc_modules(model)

        try:
            model.eval()
            # Reduced validation batches for speed
            max_val_batches = min(3, len(valid_loader))
            avg_energy, val_loss = evaluate(model, valid_loader, tokenizer, max_batches=max_val_batches, compute_metrics=False)

            # Simple energy normalization (no adaptive weighting)
            normalized_energy = normalize_energy(avg_energy, config.energy_fn_name)

            trial_time = time.time() - start_time
            logger.info(f"Trial {trial.number} completed in {trial_time:.1f}s")
            logger.info(f"  CE Loss: {val_loss:.4f}, Energy: {avg_energy:.4f}, Normalized Energy: {normalized_energy:.4f}")

            # Append trial results to single log file
            trial_log_path = f"bayesian_tuning_fast_trials.txt"
            with open(trial_log_path, "a") as f:
                f.write(f"TRIAL {trial.number} (FAST)\n")
                f.write(f"{'='*50}\n")
                f.write(f"Time: {trial_time:.1f}s | Objective: {normalized_energy:.6f}\n")
                f.write(f"CE Loss: {val_loss:.6f} | Raw Energy: {avg_energy:.6f} | Norm Energy: {normalized_energy:.6f}\n")
                f.write(f"Config: {config.energy_fn_name} | n_embed x block_size {config.n_embed}x{config.block_size} | heads={config.num_heads} | blocks={config.n_blocks} | T={config.T}\n")
                f.write(f"LR: {config.peak_learning_rate:.2e} | Warmup: {config.warmup_steps} | Dropout: {config.dropout:.3f} | Bias: {config.update_bias}\n")
                f.write(f"\n")

            trial.set_user_attr("config", config.__dict__)
            trial.set_user_attr("ce_loss", val_loss)
            trial.set_user_attr("energy", avg_energy)
            trial.set_user_attr("normalized_energy", normalized_energy)
            trial.set_user_attr("trial_time", trial_time)
            return normalized_energy  # Optimize normalized energy directly
        except Exception as e:
            logger.error(f"Evaluation failed: {str(e)}")
            import traceback
            logger.error(f"Evaluation traceback: {traceback.format_exc()}")
            return float("inf")
        
    except Exception as e:
        logger.error(f"Trial {trial.number} failed: {str(e)}")
        import traceback
        logger.error(f"Trial traceback: {traceback.format_exc()}")
        return float("inf")
    finally:
        if model is not None:
            try:
                reset_pc_modules(model)
                del model
            except:
                pass
        cleanup_memory()

def run_tuning(n_trials=30, study_name="bayesian_tuning_fast"):
    """Run fast hyperparameter tuning"""

    # Initialize detailed trials log file
    trials_log_path = f"{study_name}_trials.txt"
    with open(trials_log_path, "w") as f:
        f.write(f"DETAILED TRIAL RESULTS - {study_name}\n")
        f.write(f"{'='*50}\n")
        f.write(f"Objective: Minimize normalized energy (FAST VERSION)\n\n")

    study = optuna.create_study(
        direction='minimize',
        study_name=study_name,
        storage=f'sqlite:///{study_name}.db',
        load_if_exists=True,
        sampler=optuna.samplers.TPESampler(seed=42),
        pruner=optuna.pruners.MedianPruner(
            n_startup_trials=3,  # Reduced for faster testing
            n_warmup_steps=2,    # Reduced for faster testing
            interval_steps=1))
    
    logger.info(f"🚀 Starting FAST bayesian tuning with {n_trials} trials")
    logger.info("⚡ Using reduced data sizes for speed - FULL search space preserved")
    logger.info(f"Detailed trials log: {trials_log_path}")
    try:
        study.optimize(objective, n_trials=n_trials, show_progress_bar=False)
        
        # Results
        logger.info("Optimization completed!")
        if study.best_trial:
            trial = study.best_trial
            logger.info(f"Best trial: {trial.number}. Best value: {trial.value:.5f}")
            logger.info("Best parameters:")
            
            config_dict = trial.user_attrs.get("config")
            if config_dict:
                logger.info(
                    f"n_embed={config_dict['n_embed']}, block_size={config_dict['block_size']}, num_heads={config_dict['num_heads']} "
                    f"(head_dim={config_dict['n_embed'] // config_dict['num_heads']}), "
                    f"n_blocks={config_dict['n_blocks']}, T={config_dict['T']}, energy_fn={config_dict['energy_fn_name']}, "
                    f"update_bias={config_dict['update_bias']}, use_lateral={config_dict['use_lateral']}, "
                    f"base_lr={config_dict['local_learning_rate']:.2e}, scaled_lr={config_dict['local_learning_rate']:.2e}")

            # Save results
            results_path = f"{study_name}_results.txt"
            with open(results_path, "w") as f:
                f.write(f"FAST TESTING RESULTS\n")
                f.write(f"Best validation loss: {trial.value:.4f}\n\n")
                f.write("Best parameters:\n")
                config = trial.user_attrs.get("config")  
                
                if config:
                    f.write(f"  n_embed: {config['n_embed']}\n")
                    f.write(f"  block_size: {config['block_size']}\n")
                    f.write(f"  num_heads: {config['num_heads']}\n")
                    f.write(f"  head_dim: {config['n_embed'] // config['num_heads']}\n")
                    f.write(f"  n_blocks: {config['n_blocks']}\n")
                    f.write(f"  T: {config['T']}\n")
                    f.write(f"  dropout: {config['dropout']}\n")
                    f.write(f"  energy_fn: {config['energy_fn_name']}\n")
                    f.write(f"  update_bias: {config['update_bias']}\n")
                    f.write(f"  use_lateral: {config['use_lateral']}\n")
                    f.write(f"  scaled_lr: {config['local_learning_rate']:.2e}\n")
            
            logger.info(f"Results saved to {results_path}")
        return study
        
    except KeyboardInterrupt:
        logger.info("Optimization interrupted")
        return study

if __name__ == "__main__":
    torch.manual_seed(42)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(42)
    
    study = run_tuning(n_trials=30, study_name="bayesian_tuning_fast")
