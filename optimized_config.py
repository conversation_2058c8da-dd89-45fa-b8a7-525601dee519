"""
Optimized Configuration for PC-Transformers

This file contains improved default configurations based on analysis of 
hyperparameter tuning results and architectural considerations.
"""

from dataclasses import dataclass
from typing import Optional

@dataclass
class OptimizedGPTConfig:
    """
    Optimized configuration for PC-Transformer with better defaults.
    
    Key improvements:
    - Better parameter scaling relationships
    - Improved learning rate defaults
    - Optimized inference steps (T)
    - Better energy function choices
    - Proper head dimension constraints
    """
    
    # Model architecture
    vocab_size: int
    block_size: int = 384  # Increased from 256 for better context
    n_embed: int = 128     # Increased from 64 for better capacity
    num_heads: int = 8     # Ensures head_dim = 16 (good balance)
    n_blocks: int = 4      # Keep moderate depth
    dropout: float = 0.1   # Standard dropout
    
    # Predictive coding specific
    T: int = 7             # Reduced from 20 for efficiency (based on best results)
    local_learning_rate: float = 0.0  # Will be set dynamically
    is_holding_error: bool = True
    update_bias: bool = True
    use_lateral: bool = True
    energy_fn_name: str = "huber"  # More robust than MSE or KLD
    
    # Training parameters
    peak_learning_rate: float = 3e-4   # Higher for Adam optimizer
    warmup_steps: int = 100            # More warmup for stability
    num_epochs: int = 20
    batch_size: int = 32               # Increased from 8
    gradient_accumulation_steps: int = 2  # Effective batch size = 64
    
    # Optimization
    weight_decay: float = 0.01
    max_grad_norm: float = 1.0
    energy_weight: float = 0.1  # Weight for energy in combined loss
    
    # Scheduler
    scheduler_type: str = "cosine"  # "cosine", "linear", or "constant"
    min_lr_ratio: float = 0.01     # Minimum LR as ratio of peak LR
    
    # Special tokens
    eos_token_id: Optional[int] = None
    
    @property
    def head_dim(self) -> int:
        """Compute head dimension."""
        return self.n_embed // self.num_heads
    
    @property
    def effective_batch_size(self) -> int:
        """Compute effective batch size with gradient accumulation."""
        return self.batch_size * self.gradient_accumulation_steps
    
    def validate(self):
        """Validate configuration parameters."""
        assert self.n_embed % self.num_heads == 0, f"n_embed ({self.n_embed}) must be divisible by num_heads ({self.num_heads})"
        assert self.head_dim >= 16, f"head_dim ({self.head_dim}) should be >= 16 for good performance"
        assert self.head_dim <= 128, f"head_dim ({self.head_dim}) should be <= 128 to avoid memory issues"
        assert self.T >= 3, f"T ({self.T}) should be >= 3 for effective PC inference"
        assert self.T <= 15, f"T ({self.T}) should be <= 15 to avoid excessive computation"
        assert 0.0 <= self.dropout <= 0.5, f"dropout ({self.dropout}) should be in [0.0, 0.5]"
        assert self.energy_fn_name in ["scaled_mse", "mse", "l1", "cosine", "kld", "huber"], f"Unknown energy function: {self.energy_fn_name}"
        
    def get_scaled_lr(self, base_lr: Optional[float] = None) -> float:
        """
        Get learning rate scaled by model size.
        Larger models typically need smaller learning rates.
        """
        if base_lr is None:
            base_lr = self.peak_learning_rate
            
        # Scale by sqrt of model size (common practice)
        model_size_factor = (self.n_embed * self.n_blocks) / (128 * 4)  # Normalize to baseline
        scaled_lr = base_lr / (model_size_factor ** 0.5)
        
        return max(scaled_lr, 1e-6)  # Minimum LR threshold

# Predefined configurations for different model sizes
SMALL_CONFIG = OptimizedGPTConfig(
    vocab_size=50257,  # Will be set from tokenizer
    n_embed=64,
    num_heads=4,       # head_dim = 16
    n_blocks=2,
    T=5,
    peak_learning_rate=5e-4,
    batch_size=64,
    gradient_accumulation_steps=1
)

MEDIUM_CONFIG = OptimizedGPTConfig(
    vocab_size=50257,  # Will be set from tokenizer
    n_embed=128,
    num_heads=8,       # head_dim = 16
    n_blocks=4,
    T=7,
    peak_learning_rate=3e-4,
    batch_size=32,
    gradient_accumulation_steps=2
)

LARGE_CONFIG = OptimizedGPTConfig(
    vocab_size=50257,  # Will be set from tokenizer
    n_embed=256,
    num_heads=16,      # head_dim = 16
    n_blocks=6,
    T=7,
    peak_learning_rate=2e-4,
    batch_size=16,
    gradient_accumulation_steps=4
)

def get_config_by_size(size: str, vocab_size: int) -> OptimizedGPTConfig:
    """Get configuration by size name."""
    configs = {
        "small": SMALL_CONFIG,
        "medium": MEDIUM_CONFIG,
        "large": LARGE_CONFIG
    }
    
    if size not in configs:
        raise ValueError(f"Unknown size: {size}. Choose from {list(configs.keys())}")
    
    config = configs[size]
    config.vocab_size = vocab_size
    config.validate()
    
    return config

def get_best_config_from_tuning(vocab_size: int) -> OptimizedGPTConfig:
    """
    Get configuration based on best hyperparameter tuning results.
    Based on analysis of bayesian_tuning_fast_results.txt and BEST_params.txt
    """
    config = OptimizedGPTConfig(
        vocab_size=vocab_size,
        block_size=384,        # From best results
        n_embed=128,           # Scaled up from 64 for better capacity
        num_heads=8,           # Ensures head_dim = 16
        n_blocks=4,            # Good balance
        T=7,                   # From best results
        dropout=0.15,          # From best results
        energy_fn_name="kld",  # From best results
        update_bias=True,      # From best results
        use_lateral=True,      # From best results
        peak_learning_rate=3e-4,  # Scaled for Adam
        warmup_steps=100,
        batch_size=32,
        gradient_accumulation_steps=2
    )
    
    config.validate()
    return config
