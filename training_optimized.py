"""
Optimized Training Script for PC-Transformers

This script implements all the major optimizations identified:
1. Global optimizer (AdamW) + local PC updates
2. Improved learning rate scheduling
3. Combined loss (CE + normalized energy)
4. Gradient clipping and accumulation
5. Better numerical stability
6. Enhanced monitoring and logging
"""

import os
import torch
import math
import time
import torch.nn.functional as F
from torch.optim import AdamW
from torch.optim.lr_scheduler import CosineAnnealingLR, LinearLR, SequentialLR
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator

from optimized_config import OptimizedGPTConfig, get_best_config_from_tuning
from model_architecture.pc_t_model import PCTransformer
from Data_preprocessing.dataloader import train_loader, valid_loader
from utils.model_utils import load_tokenizer, reset_pc_modules
from predictive_coding.pc_layer import PCLayer
from eval import evaluate

def create_optimizer_and_scheduler(model, config, total_steps):
    """Create optimizer and learning rate scheduler."""
    # Separate parameters for different learning rates
    pc_params = []
    main_params = []
    
    for name, param in model.named_parameters():
        if 'W_latents' in name or 'pc_layer' in name:
            pc_params.append(param)
        else:
            main_params.append(param)
    
    # Create optimizer with parameter groups
    optimizer = AdamW([
        {'params': main_params, 'lr': config.peak_learning_rate, 'weight_decay': config.weight_decay},
        {'params': pc_params, 'lr': config.peak_learning_rate * 0.1, 'weight_decay': 0.0}  # Lower LR for PC params
    ])
    
    # Create scheduler
    if config.scheduler_type == "cosine":
        scheduler = CosineAnnealingLR(
            optimizer, 
            T_max=total_steps, 
            eta_min=config.peak_learning_rate * config.min_lr_ratio
        )
    elif config.scheduler_type == "linear":
        warmup_scheduler = LinearLR(optimizer, start_factor=0.1, total_iters=config.warmup_steps)
        main_scheduler = LinearLR(optimizer, start_factor=1.0, end_factor=config.min_lr_ratio, 
                                 total_iters=total_steps - config.warmup_steps)
        scheduler = SequentialLR(optimizer, [warmup_scheduler, main_scheduler], [config.warmup_steps])
    else:  # constant
        scheduler = None
    
    return optimizer, scheduler

def normalize_energy(energy, energy_fn_name):
    """Normalize energy based on function type for stable training."""
    if energy_fn_name == "scaled_mse":
        return energy / 0.1  # Undo scaling
    elif energy_fn_name == "kld":
        return energy / 5.0  # Scale down KLD
    elif energy_fn_name == "huber":
        return energy / 0.5  # Scale down Huber
    else:
        return energy

def train_epoch(model, dataloader, optimizer, scheduler, config, epoch):
    """Train for one epoch with improved optimization."""
    model.train()
    total_ce_loss = 0.0
    total_energy = 0.0
    total_combined_loss = 0.0
    batch_count = 0
    
    tokenizer = load_tokenizer()
    pad_token_id = tokenizer.pad_token_id
    vocab_size = tokenizer.vocab_size
    
    for batch_idx, batch in enumerate(dataloader):
        input_ids = batch["input_ids"]
        target_ids = batch["target_ids"]
        
        # Clip target_ids to valid range
        if target_ids.max() >= vocab_size:
            target_ids = torch.clamp(target_ids, max=vocab_size-1)
        
        # Update local learning rates for PC layers
        current_lr = scheduler.get_last_lr()[0] if scheduler else config.peak_learning_rate
        for module in model.modules():
            if hasattr(module, 'local_lr'):
                module.local_lr = current_lr * 0.1  # PC layers use 10% of global LR
        
        # Forward pass
        logits = model(target_ids, input_ids)
        
        # Compute CE loss
        ce_loss = F.cross_entropy(
            logits.view(-1, logits.size(-1)),
            target_ids.view(-1),
            ignore_index=pad_token_id
        )
        
        # Collect and normalize PC energies
        layer_energies = []
        for module in model.modules():
            if isinstance(module, PCLayer) and hasattr(module, '_energy'):
                if module._energy is not None and not torch.isnan(torch.tensor(module._energy)):
                    layer_energies.append(module._energy)
        
        if layer_energies:
            avg_energy = sum(layer_energies) / len(layer_energies)
            normalized_energy = normalize_energy(avg_energy, config.energy_fn_name)
        else:
            avg_energy = 0.0
            normalized_energy = 0.0
        
        # Combined loss
        combined_loss = ce_loss + config.energy_weight * normalized_energy
        
        # Scale loss for gradient accumulation
        scaled_loss = combined_loss / config.gradient_accumulation_steps
        scaled_loss.backward()
        
        # Gradient accumulation
        if (batch_idx + 1) % config.gradient_accumulation_steps == 0:
            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(model.parameters(), config.max_grad_norm)
            
            # Optimizer step
            optimizer.step()
            optimizer.zero_grad()
            
            # Scheduler step
            if scheduler:
                scheduler.step()
        
        # Statistics
        total_ce_loss += ce_loss.item()
        total_energy += avg_energy
        total_combined_loss += combined_loss.item()
        batch_count += 1
        
        # Reset PC modules
        reset_pc_modules(model)
        
        # Logging
        if (batch_idx + 1) % 10 == 0:
            perplexity = math.exp(ce_loss.item()) if ce_loss.item() < 100 else float("inf")
            print(f"  Batch {batch_idx + 1}/{len(dataloader)} | "
                  f"CE: {ce_loss.item():.4f} | Energy: {avg_energy:.4f} | "
                  f"Combined: {combined_loss.item():.4f} | PPL: {perplexity:.2f} | "
                  f"LR: {current_lr:.6f}")
    
    # Return averages
    avg_ce_loss = total_ce_loss / batch_count if batch_count > 0 else 0.0
    avg_energy = total_energy / batch_count if batch_count > 0 else 0.0
    avg_combined_loss = total_combined_loss / batch_count if batch_count > 0 else 0.0
    avg_perplexity = math.exp(avg_ce_loss) if avg_ce_loss < 100 else float("inf")
    
    return avg_ce_loss, avg_energy, avg_combined_loss, avg_perplexity

def main():
    """Main training function with all optimizations."""
    print("========== Optimized PC-Transformer Training ==========")
    
    # Load tokenizer and create optimized config
    tokenizer = load_tokenizer()
    vocab_size = tokenizer.vocab_size
    
    # Use best configuration from hyperparameter tuning
    config = get_best_config_from_tuning(vocab_size)
    config.eos_token_id = tokenizer.eos_token_id
    
    print(f"Configuration:")
    print(f"  Model size: {config.n_embed}d, {config.num_heads}h, {config.n_blocks}L")
    print(f"  Head dim: {config.head_dim}")
    print(f"  PC steps: {config.T}")
    print(f"  Energy function: {config.energy_fn_name}")
    print(f"  Effective batch size: {config.effective_batch_size}")
    print(f"  Learning rate: {config.peak_learning_rate}")
    
    # Create model
    model = PCTransformer(config)
    total_params = sum(p.numel() for p in model.parameters())
    print(f"  Total parameters: {total_params:,}")
    
    # Create optimizer and scheduler
    total_steps = len(train_loader) * config.num_epochs // config.gradient_accumulation_steps
    optimizer, scheduler = create_optimizer_and_scheduler(model, config, total_steps)
    
    print(f"  Total training steps: {total_steps}")
    print("=" * 60)
    
    # Training loop
    train_losses = []
    train_energies = []
    train_combined_losses = []
    train_perplexities = []
    val_losses = []
    val_energies = []
    
    start_time = time.time()
    
    for epoch in range(config.num_epochs):
        print(f"\nEpoch {epoch + 1}/{config.num_epochs}")
        
        # Training
        train_ce, train_energy, train_combined, train_ppl = train_epoch(
            model, train_loader, optimizer, scheduler, config, epoch
        )
        
        # Validation
        model.eval()
        with torch.no_grad():
            val_energy, val_ce = evaluate(model, valid_loader, tokenizer, max_batches=10, compute_metrics=False)
        
        # Store metrics
        train_losses.append(train_ce)
        train_energies.append(train_energy)
        train_combined_losses.append(train_combined)
        train_perplexities.append(train_ppl)
        val_losses.append(val_ce)
        val_energies.append(val_energy)
        
        # Epoch summary
        print(f"Epoch {epoch + 1} Summary:")
        print(f"  Train - CE: {train_ce:.4f}, Energy: {train_energy:.4f}, Combined: {train_combined:.4f}, PPL: {train_ppl:.2f}")
        print(f"  Valid - CE: {val_ce:.4f}, Energy: {val_energy:.4f}")
    
    total_time = time.time() - start_time
    print(f"\nTraining completed in {total_time:.2f} seconds")
    
    # Save model
    save_path = "checkpoints/pc_transformer_optimized.pt"
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    torch.save({
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'config': config,
        'train_losses': train_losses,
        'train_energies': train_energies,
        'train_combined_losses': train_combined_losses,
        'train_perplexities': train_perplexities,
        'val_losses': val_losses,
        'val_energies': val_energies,
        'total_time': total_time
    }, save_path)
    print(f"Model saved to {save_path}")
    
    # Plot results
    plot_training_results(train_losses, train_energies, train_combined_losses, 
                         train_perplexities, val_losses, val_energies)

def plot_training_results(train_losses, train_energies, train_combined_losses, 
                         train_perplexities, val_losses, val_energies):
    """Plot comprehensive training results."""
    epochs = list(range(1, len(train_losses) + 1))
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    
    # Loss comparison
    ax1.plot(epochs, train_losses, 'b-', label='Train CE Loss', marker='o')
    ax1.plot(epochs, val_losses, 'r-', label='Val CE Loss', marker='s')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('CE Loss')
    ax1.set_title('Cross-Entropy Loss')
    ax1.legend()
    ax1.grid(True)
    
    # Energy comparison
    ax2.plot(epochs, train_energies, 'g-', label='Train Energy', marker='^')
    ax2.plot(epochs, val_energies, 'orange', label='Val Energy', marker='v')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Energy')
    ax2.set_title('PC Energy')
    ax2.legend()
    ax2.grid(True)
    
    # Combined loss
    ax3.plot(epochs, train_combined_losses, 'purple', label='Combined Loss', marker='d')
    ax3.set_xlabel('Epoch')
    ax3.set_ylabel('Combined Loss')
    ax3.set_title('Combined Loss (CE + Energy)')
    ax3.legend()
    ax3.grid(True)
    
    # Perplexity
    ax4.plot(epochs, train_perplexities, 'brown', label='Train Perplexity', marker='*')
    ax4.set_xlabel('Epoch')
    ax4.set_ylabel('Perplexity')
    ax4.set_title('Perplexity')
    ax4.legend()
    ax4.grid(True)
    ax4.set_yscale('log')
    
    for ax in [ax1, ax2, ax3, ax4]:
        ax.xaxis.set_major_locator(MaxNLocator(integer=True))
    
    plt.tight_layout()
    plt.savefig('assets/optimized_training_results.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    main()
