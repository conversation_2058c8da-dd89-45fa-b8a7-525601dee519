#!/usr/bin/env python3
"""
Merge Bayesian tuning results from multiple GPUs into a single study.
Usage: python tuning/merge_gpu_results.py --study_name bayesian_tuning --num_gpus 2
"""

import argparse
import optuna
import os
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def merge_gpu_studies(study_name, num_gpus, output_name=None):
    """Merge studies from multiple GPUs into a single consolidated study"""
    
    if output_name is None:
        output_name = f"{study_name}_merged"
    
    # Create merged study
    merged_storage = f"sqlite:///tuning/{output_name}.db"
    merged_study = optuna.create_study(
        direction='minimize',
        study_name=output_name,
        storage=merged_storage,
        load_if_exists=True
    )
    
    all_trials = []
    total_trials = 0
    
    # Collect trials from all GPU studies
    for rank in range(num_gpus):
        gpu_storage = f"sqlite:///tuning/{study_name}_rank{rank}.db"
        gpu_study_name = f"{study_name}_rank{rank}"
        
        if not os.path.exists(f"tuning/{study_name}_rank{rank}.db"):
            logger.warning(f"Database not found for rank {rank}: {gpu_storage}")
            continue
            
        try:
            gpu_study = optuna.load_study(
                study_name=gpu_study_name,
                storage=gpu_storage
            )
            
            gpu_trials = len(gpu_study.trials)
            total_trials += gpu_trials
            logger.info(f"GPU {rank}: {gpu_trials} trials")
            
            # Add trials to merged study
            for trial in gpu_study.trials:
                if trial.state == optuna.trial.TrialState.COMPLETE:
                    # Create new trial in merged study with same parameters and value
                    merged_trial = merged_study.ask()
                    
                    # Set parameters
                    for param_name, param_value in trial.params.items():
                        merged_trial.suggest_categorical(param_name, [param_value])
                    
                    # Set user attributes
                    for attr_name, attr_value in trial.user_attrs.items():
                        merged_trial.set_user_attr(attr_name, attr_value)
                    
                    # Complete trial with same value
                    merged_study.tell(merged_trial, trial.value)
                    
        except Exception as e:
            logger.error(f"Error loading study for rank {rank}: {e}")
            continue
    
    logger.info(f"Merged {total_trials} trials from {num_gpus} GPUs")
    
    # Find and report best trial
    if merged_study.best_trial:
        best = merged_study.best_trial
        logger.info(f"Best trial: {best.number}")
        logger.info(f"Best value: {best.value:.6f}")
        logger.info(f"Best params: {best.params}")
        
        # Save best parameters
        best_params_file = f"tuning/{output_name}_best_params.txt"
        with open(best_params_file, "w") as f:
            f.write(f"BEST PARAMETERS - {output_name}\n")
            f.write(f"{'='*50}\n\n")
            f.write(f"Trial: {best.number}\n")
            f.write(f"Objective Value: {best.value:.6f}\n\n")
            f.write(f"Parameters:\n")
            for param, value in best.params.items():
                f.write(f"  {param}: {value}\n")
            f.write(f"\nUser Attributes:\n")
            for attr, value in best.user_attrs.items():
                f.write(f"  {attr}: {value}\n")
        
        logger.info(f"Best parameters saved to: {best_params_file}")
    
    return merged_study

def main():
    parser = argparse.ArgumentParser(description="Merge GPU Bayesian tuning results")
    parser.add_argument("--study_name", default="bayesian_tuning", help="Base study name")
    parser.add_argument("--num_gpus", type=int, required=True, help="Number of GPUs used")
    parser.add_argument("--output_name", help="Output study name (default: {study_name}_merged)")
    
    args = parser.parse_args()
    
    logger.info(f"Merging results from {args.num_gpus} GPUs")
    logger.info(f"Study name: {args.study_name}")
    
    merged_study = merge_gpu_studies(
        study_name=args.study_name,
        num_gpus=args.num_gpus,
        output_name=args.output_name
    )
    
    logger.info("Merge completed successfully!")

if __name__ == "__main__":
    main()
